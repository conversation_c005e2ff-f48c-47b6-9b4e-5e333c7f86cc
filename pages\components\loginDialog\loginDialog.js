// components/auditDialog/auditDialog.js// components/dialog/dialog.js
var util = require('../../../utils/util')
var api = require('../../../config/api.js')
var app = getApp()
Component({
  /**
   * 组件的一些选项
   */
  options: {
    addGlobalClass: true,
  },
  /**
   * 组件的属性列表，父组件传值过来要在这设置
   */
  properties: {
    pageName: {
      type: String,
    },
    title: {
      type: String,
      default: '标题',
    },
    content: String,
    showModal: {
      type: Boolean,
      default: false,
    },
    visitor: {
      type: Boolean,
      default: false,
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    yesIconUrl: 'yes-ico', //yes-ico yes-icon-select
    allowXY: false,
    id: '',
    showGetNumber: false,
    isVisitor: false,
  },
  observers: {
    'visitor': function (val) {
      this.setData({
        isVisitor: val,
      })
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    back() {
      this.setData({
        showModal: false,
      })
    },
    allow() {
      if (this.data.allowXY) {
        this.setData({
          allowXY: false,
        })
      } else {
        this.setData({
          allowXY: true,
        })
      }
    },
    sqbd() {
      util.showToast('请阅读并同意服务协议、隐私政策协议及登录政策')
    },
    getPhoneNumber: function (e) {
      var that = this
      console.log(e.detail.errMsg == 'getPhoneNumber:ok')
      util.showLoading('正在加载…')
      if (e.detail.errMsg == 'getPhoneNumber:ok') {
        util
          .request(
            api.login,
            {
              code: e.detail.code,
            },
            'POST'
          )
          .then(function (res) {
            if (res.code == '0') {
              //注册成功
              var result = res.result
              app.globalData.userInfo = result
              try {
                wx.setStorageSync('token', result.token)
                wx.setStorageSync('loginStatus', result.loginStatus)
              } catch (e) {
                console.log(e)
              }
              wx.hideLoading()
              if (that.data.isVisitor) {
                that.setData({
                  showModal: false,
                })
                that.triggerEvent('refresh') //通知父页面完成注册刷新数据
              } else {
                if (result.userStatus == 'REGISTERED') {
                  //已注册但未绑定车牌
                  that.setData({
                    showGetNumber: true,
                    showModal: false,
                  })
                } else {
                  that.setData({
                    showModal: false,
                  })
                  that.triggerEvent('refresh') //通知父页面完成注册刷新数据
                }
              }
            } else {
              wx.hideLoading()
              util.showToast(res.message)
            }
          })
      } else {
        this.setData({
          showModal: false,
        })
        wx.hideLoading()
        util.showToast('申请获取手机号失败！')
      }
    },
    handleCancel() {
      //调用父组件方法
      this.triggerEvent('cancel')
    },
    handleConfirm() {
      let name = this.data.name
      let examineReason = this.data.examineReason
      let id = this.data.id
      //调用父组件方法并传值给父组件
      this.triggerEvent('confirm', {
        name,
        examineReason,
        id,
      })
    },

    getLeftGo: function (e) {
      //取消
      this.setData({
        showGetNumber: false,
      })
      this.triggerEvent('refresh')
    },
    getRightGo: function (e) {
      //去添加
      wx.navigateTo({
        url: '/pages/my/myCar/index',
        fail: function (res) {
          console.log(res)
        },
      })
    },
    viewAgreement: function (e) {
      var id = e.currentTarget.id
      wx.navigateTo({
        url: '/pages/common/viewAgreement/index?id=' + id,
        fail: function (res) {
          console.log(res)
        },
      })
    },
  },
})
