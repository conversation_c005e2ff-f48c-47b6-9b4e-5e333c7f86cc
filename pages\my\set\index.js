// pages/set/set.js
var util = require('../../../utils/util')
var api = require('../../../config/api.js')
var app = getApp()
Page({
  data: {
    loginStatus: app.globalData.userInfo.loginStatus, //登陆状态
    list: [
      {
        name: '关于',
        id: '',
      },
      {
        name: '注销账号',
        id: '',
      },
    ],
  },
  onShow() {
    this.setData({
      loginStatus: app.globalData.userInfo.loginStatus,
    })
  },
  logout() {
    util.showLoading('正在退出，请稍候')
    util.request(api.logout, '', 'POST').then(function (res) {
      if (res.code == '0') {
        try {
          app.globalData.userInfo = res.result
          wx.setStorageSync('token', res.result.token)
          wx.setStorageSync('loginStatus', '')
        } catch (e) {
          console.log(e)
        }
        wx.hideLoading()
        wx.switchTab({
          url: '/pages/my/my',
          fail: function (res) {
            console.log('退出登陆跳转失败：' + res.errMsg)
          },
        })
      } else {
        util.showToast(res.message)
        wx.hideLoading()
      }
    })
  },
  logoff() {
    wx.navigateTo({
      url: '/pages/my/set/logoff/logoffCheck/index',
    })
  },
  gotoPage(e) {
    let url = e.currentTarget.dataset.url
    console.log(url)
    if (!url) {
      return
    }
    var check = e.currentTarget.dataset.check

    if (check == 'true') {
      if (app.globalData.userInfo.loginStatus == 'LOGGED_IN') {
        //校验是否已登陆
        wx.navigateTo({
          url: url,
        })
      } else {
        wx.navigateTo({
          url: '/pages/common/loginTip/loginTip?tiptype=0&&route=' + url,
        })
      }
    } else {
      wx.navigateTo({
        url: url,
      })
    }
  },
  refresh() {
    //登录后还是保留在原来页面，详情请见Q群聊天记录
    this.setData({
      loginStatus: app.globalData.userInfo.loginStatus,
    })
  },
})
