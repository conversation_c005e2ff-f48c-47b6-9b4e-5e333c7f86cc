var app = getApp()
var api = require('../config/api.js')
const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}
/**
 * 封封微信的的request
 */
function request(url, data = {}, method = 'GET') {
  return new Promise(function (resolve, reject) {
    wx.request({
      url: url,
      data: data,
      method: method,
      header: {
        'Content-Type': 'application/json',
        'token': wx.getStorageSync('token'),
        'openId': wx.getStorageSync('openId'),
      },
      success: function (res) {
        if (res.statusCode == 200) {
          resolve(res.data)
        } else {
          reject(res.message || res)
        }
      },
      fail: function (err) {
        reject(err)
      },
    })
  })
}

function showLoading(title) {
  wx.showLoading({
    title: title,
    mask: true,
  })
}

function showToast(msg) {
  if (msg != '未登录') {
    wx.showToast({
      title: msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
// rpx转px
function rpxToPx(v_rpx) {
  var oneRpxToPx = 750 / app.globalData.windowWidth
  var v_px = v_rpx / oneRpxToPx
  return v_px
}
// px转rpx
function pxToRpx(v_px) {
  var onePxToRpx = 750 / app.globalData.windowWidth
  var v_rpx = v_px * onePxToRpx
  return v_rpx
}

function openMapApp(latitude, longitude, scale, name, address) {
  wx.openLocation({
    longitude: longitude,
    latitude: latitude,
    scale: scale,
    name: name,
    address: address,
    fail(res) {
      console.log(res)
    },
  })
}

function JumpAdvertisement(url) {
  wx.navigateTo({
    url: url,
  })
}

function getPayMethod() {
  //获取支付方式
  wx.showLoading({
    title: '正在加载…',
  })
  request(api.getPayMethod + '/ARREARS', {}, 'GET').then(function (res) {
    var list = res.result
    if (res.code == '0') {
      console.log('获取支付方式成功')
      app.globalData.payList = list
      if (list.length > 0) {
        var payType = list[0]['payType']
        console.log('获取支付方式成功---默认支付类型' + payType)
        app.globalData.selectPay = payType
        if (payType === 'BALANCE_PAY') {
          app.globalData.balance = list[0]['iconUrl'] //获取当前余额
          console.log('获取支付方式成功---当前余额' + list[0]['iconUrl'])
        }
      }
      wx.hideLoading()
    } else {
      console.log('获取支付方式失败' + res.message)
      app.globalData.payList = []
      wx.hideLoading()
    }
  })
}
function viewAgreement(id) {
  wx.navigateTo({
    url: '/pages/common/viewAgreement/index?id=' + id,
    fail: function (res) {
      console.log(res)
    },
  })
}

const getURLParameters = url =>
  (url.match(/([^?=&]+)(=([^&]*))/g) || []).reduce(
    (a, v) => ((a[v.slice(0, v.indexOf('='))] = v.slice(v.indexOf('=') + 1)), a),
    {}
  )

const fetchToken = () => {
  return new Promise((resolve, reject) => {
    wx.login({
      //调用登陆接口获取token
      success: res => {
        if (res.code) {
          request(
            api.getLoginInfo,
            {
              code: res.code,
            },
            'GET'
          )
            .then(function (res) {
              if (res.code == '0') {
                const info = res.result
                wx.setStorageSync('token', info.token)
                wx.setStorageSync('loginStatus', info.loginStatus)
                app.globalData.userInfo = info
                resolve(info)
              } else {
                showToast(res.message)
                reject(res.message)
              }
            })
            .catch(err => {
              reject(err)
            })
        } else {
          console.log('登录失败！' + res.errMsg)
          reject(res.errMsg)
        }
      },
      fail(err) {
        reject(err)
      },
    })
  })
}

const fetchTokenSC = () => {
  return new Promise((resolve, reject) => {
    wx.login({
      //调用登陆接口获取token
      success: res => {
        if (res.code) {
          request(
            api.getWxOpenid,
            {
              JsCode: res.code,
            },
            'GET'
          )
            .then(function (res) {
              if (res.code == '200') {
                wx.setStorageSync('openId', res.result.openId)
              } else {
                reject(res.message)
              }
            })
            .catch(err => {
              reject(err)
            })
        } else {
          console.log('登录失败！' + res.errMsg)
          reject(res.errMsg)
        }
      },
      fail(err) {
        reject(err)
      },
    })
  })
}

/**
 * @description 生成唯一 UUID
 * @return string
 */
function generateUUID() {
  if (typeof crypto === 'object') {
    if (typeof crypto.randomUUID === 'function') {
      return crypto.randomUUID()
    }
    if (typeof crypto.getRandomValues === 'function' && typeof Uint8Array === 'function') {
      const callback = c => {
        const num = Number(c)
        return (num ^ (crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (num / 4)))).toString(16)
      }
      return '10000000-1000-4000-8000-100000000000'.replace(/[018]/g, callback)
    }
  }

  let timestamp = new Date().getTime()
  let performanceNow = (typeof performance !== 'undefined' && performance.now && performance.now() * 1000) || 0

  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    let random = Math.random() * 16
    if (timestamp > 0) {
      random = (timestamp + random) % 16 | 0
      timestamp = Math.floor(timestamp / 16)
    } else {
      random = (performanceNow + random) % 16 | 0
      performanceNow = Math.floor(performanceNow / 16)
    }
    return (c === 'x' ? random : (random & 0x3) | 0x8).toString(16)
  })
}

/**
 * 生成指定长度的随机数字字符串。
 * @returns {string} 随机数字字符串，长度为32位。
 */
function generateSeq() {
  const length = 32 // 指定生成随机数的长度
  const chars = '0123456789' // 可以使用的字符集
  let result = ''
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * chars.length)
    result += chars[randomIndex]
  }
  return result
}

/**
 * 对停车场列表按"节假日标识"优先排序，并添加节假日免费标记
 * @param {Array} lots 停车场数组
 * @returns {Array} 排序后的数组
 */
function sortParkingLotsByHoliday(lots) {
  if (!Array.isArray(lots)) return []

  const holidayRegex = /节假日标识\((.*?)\)/

  // 添加节假日免费标记并返回排序后的数组
  return lots
    .map(item => {
      if (item.description) {
        const match = item.description.match(holidayRegex)
        if (match) {
          return {
            ...item,
            isHolidayFree: true,
            holidayDesc: match[1],
          }
        }
      }
      return item
    })
    .sort((a, b) => {
      const aHasHoliday = a.description?.match(holidayRegex)
      const bHasHoliday = b.description?.match(holidayRegex)
      return (bHasHoliday ? 1 : 0) - (aHasHoliday ? 1 : 0)
    })
}

module.exports = {
  viewAgreement,
  getPayMethod,
  JumpAdvertisement,
  openMapApp,
  pxToRpx,
  rpxToPx,
  formatTime,
  request,
  showLoading,
  showToast,
  getURLParameters,
  fetchToken,
  fetchTokenSC,
  generateUUID,
  generateSeq,
  sortParkingLotsByHoliday,
}
